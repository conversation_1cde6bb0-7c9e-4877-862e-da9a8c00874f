#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像质量可视化分析演示程序
展示类似您提供示例的图像质量分析界面
"""

import sys
import os
import numpy as np
sys.path.append('src')

from gas_meter_detector import analyze_gas_meter_image
from ocr_visualization import create_comprehensive_visual_analysis, create_comparison_visual_analysis
import json
from pathlib import Path
from datetime import datetime

def get_image_path_interactive(prompt: str) -> str:
    """交互式获取图像路径"""
    while True:
        path = input(prompt).strip().strip('"\'')
        if not path:
            print("❌ 路径不能为空，请重新输入")
            continue
        
        if not os.path.exists(path):
            print(f"❌ 文件不存在: {path}")
            continue
        
        if not path.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff')):
            print("❌ 请输入有效的图像文件 (.jpg, .jpeg, .png, .bmp, .tiff)")
            continue
        
        return path

def single_image_analysis():
    """单张图像质量分析"""
    print("\n" + "="*60)
    print("🔍 单张图像质量分析")
    print("="*60)
    
    try:
        # 获取图像路径
        image_path = get_image_path_interactive("📁 请输入图像文件路径: ")
        
        print(f"\n🔍 开始分析图像: {Path(image_path).name}")
        print("-" * 60)
        
        # 分析图像（禁用OCR）
        result = analyze_gas_meter_image(image_path)
        
        # 显示分析结果摘要
        print("\n📊 分析结果摘要:")
        print(f"   文件名: {result['file_info']['file_name']}")
        print(f"   图像尺寸: {result['file_info']['width']} × {result['file_info']['height']}")
        print(f"   文件大小: {result['file_info']['file_size_mb']:.2f} MB")
        print(f"   综合得分: {result['summary']['overall_score']:.3f}")
        print(f"   质量等级: {result['summary']['quality_grade']}")
        print(f"   OCR适用性: {'✅' if result['summary']['is_suitable_for_gas_meter'] else '❌'}")
        
        # 详细质量指标 - 安全访问
        quality_analysis = result.get('quality_analysis', {})
        if isinstance(quality_analysis, dict):
            digit_clarity = quality_analysis.get('digit_clarity_check', {})
            lighting_check = quality_analysis.get('lighting_check', {})

            if isinstance(digit_clarity, dict) and isinstance(lighting_check, dict):
                print(f"\n📋 详细质量指标:")
                print(f"   数字清晰度: {digit_clarity.get('digit_clarity_score', 0):.3f}")
                print(f"   边缘清晰度: {digit_clarity.get('edge_clarity', 0):.2f}")
                print(f"   对比度: {digit_clarity.get('contrast', 0):.1f}")
                print(f"   局部方差: {digit_clarity.get('local_variance', 0):.2f}")
                print(f"   边缘密度: {digit_clarity.get('edge_density', 0):.4f}")
                print(f"   平均亮度: {lighting_check.get('brightness', 0):.1f}")
                print(f"   光线质量: {lighting_check.get('lighting_score', 0):.3f}")
            else:
                print(f"\n📋 详细质量指标: 数据格式错误，无法显示详细信息")
        else:
            print(f"\n📋 详细质量指标: 质量分析数据不可用")
        
        # 询问是否生成可视化分析图
        print(f"\n🎨 是否生成可视化分析图？")
        generate_visual = input("输入 y 生成，n 跳过: ").strip().lower()

        visual_path = None
        if generate_visual in ['y', 'yes', '是']:
            print(f"🎨 正在生成可视化分析图...")
            visual_path = create_comprehensive_visual_analysis(image_path, result)

            if visual_path:
                print(f"✅ 可视化分析完成！")
                print(f"📁 保存位置: {visual_path}")
            else:
                print(f"❌ 可视化分析图生成失败")
        else:
            print(f"⏭️ 跳过可视化生成")

        # 保存详细分析结果 - JSON安全版本
        output_dir = Path("output_results")
        output_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_path = output_dir / f"analysis_result_{Path(image_path).stem}_{timestamp}.json"

        # 创建JSON安全的结果数据
        json_safe_result = {
            "file_info": {
                "file_path": str(result.get('file_info', {}).get('file_path', '')),
                "file_name": str(result.get('file_info', {}).get('file_name', '')),
                "file_size_mb": float(result.get('file_info', {}).get('file_size_mb', 0)),
                "width": int(result.get('file_info', {}).get('width', 0)),
                "height": int(result.get('file_info', {}).get('height', 0))
            },
            "summary": {
                "overall_score": float(result.get('summary', {}).get('overall_score', 0)),
                "quality_grade": str(result.get('summary', {}).get('quality_grade', '')),
                "is_suitable_for_gas_meter": bool(result.get('summary', {}).get('is_suitable_for_gas_meter', False))
            },
            "analysis_time": timestamp
        }

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_safe_result, f, ensure_ascii=False, indent=2)

        print(f"💾 详细分析结果已保存至: {json_path}")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        return False
    
    return True

def comparison_analysis():
    """两张图像对比分析"""
    print("\n" + "="*60)
    print("📊 两张图像对比分析")
    print("="*60)
    
    try:
        # 获取两张图像路径
        image1_path = get_image_path_interactive("📁 请输入第一张图像路径: ")
        image2_path = get_image_path_interactive("📁 请输入第二张图像路径: ")
        
        print(f"\n🔍 开始对比分析...")
        print(f"图像1: {Path(image1_path).name}")
        print(f"图像2: {Path(image2_path).name}")
        print("-" * 60)
        
        # 分析两张图像（禁用OCR）
        print("🔍 分析图像1...")
        result1 = analyze_gas_meter_image(image1_path)

        print("🔍 分析图像2...")
        result2 = analyze_gas_meter_image(image2_path)
        
        # 显示对比结果摘要
        print("\n📊 对比结果摘要:")
        print(f"📋 图像1: {result1['file_info']['file_name']}")
        print(f"   综合得分: {result1['summary']['overall_score']:.3f}")
        print(f"   质量等级: {result1['summary']['quality_grade']}")
        
        print(f"\n📋 图像2: {result2['file_info']['file_name']}")
        print(f"   综合得分: {result2['summary']['overall_score']:.3f}")
        print(f"   质量等级: {result2['summary']['quality_grade']}")
        
        # 计算差值
        score_diff = result2['summary']['overall_score'] - result1['summary']['overall_score']
        print(f"\n📈 质量差值: {score_diff:+.3f}")
        if score_diff > 0:
            print("   图像2质量更优")
        elif score_diff < 0:
            print("   图像1质量更优")
        else:
            print("   两图像质量相当")
        
        # 询问是否生成对比可视化分析图
        print(f"\n🎨 是否生成对比可视化分析图？")
        generate_visual = input("输入 y 生成，n 跳过: ").strip().lower()

        visual_path = None
        if generate_visual in ['y', 'yes', '是']:
            print(f"🎨 正在生成对比可视化分析图...")
            visual_path = create_comparison_visual_analysis(image1_path, image2_path, result1, result2)

            if visual_path:
                print(f"✅ 对比可视化分析完成！")
                print(f"📁 保存位置: {visual_path}")
            else:
                print(f"❌ 对比可视化分析图生成失败")
        else:
            print(f"⏭️ 跳过对比可视化生成")

        # 保存对比分析结果
        output_dir = Path("output_results")
        output_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建JSON安全的对比结果
        comparison_result = {
            "timestamp": timestamp,
            "image1": {
                "path": image1_path,
                "summary": {
                    "overall_score": float(result1.get('summary', {}).get('overall_score', 0)),
                    "quality_grade": str(result1.get('summary', {}).get('quality_grade', '')),
                    "is_suitable_for_gas_meter": bool(result1.get('summary', {}).get('is_suitable_for_gas_meter', False))
                }
            },
            "image2": {
                "path": image2_path,
                "summary": {
                    "overall_score": float(result2.get('summary', {}).get('overall_score', 0)),
                    "quality_grade": str(result2.get('summary', {}).get('quality_grade', '')),
                    "is_suitable_for_gas_meter": bool(result2.get('summary', {}).get('is_suitable_for_gas_meter', False))
                }
            },
            "comparison": {
                "score_difference": float(score_diff),
                "better_image": "图像2" if score_diff > 0 else "图像1" if score_diff < 0 else "相当"
            }
        }
        
        json_path = output_dir / f"comparison_result_{timestamp}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(comparison_result, f, ensure_ascii=False, indent=2)
        
        print(f"💾 对比分析结果已保存至: {json_path}")
        
    except Exception as e:
        print(f"❌ 对比分析过程中出现错误: {str(e)}")
        return False
    
    return True

def folder_batch_analysis():
    """文件夹批量分析"""
    print("\n🗂️ 文件夹批量分析")
    print("-" * 40)

    # 获取文件夹路径
    folder_path = input("📁 请输入文件夹路径: ").strip().strip('"')

    if not folder_path:
        print("❌ 文件夹路径不能为空")
        return False

    folder_path = Path(folder_path)
    if not folder_path.exists():
        print(f"❌ 文件夹不存在: {folder_path}")
        return False

    if not folder_path.is_dir():
        print(f"❌ 路径不是文件夹: {folder_path}")
        return False

    # 查找所有图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    image_files = []

    for ext in image_extensions:
        image_files.extend(folder_path.glob(f"*{ext}"))
        image_files.extend(folder_path.glob(f"*{ext.upper()}"))

    if not image_files:
        print(f"❌ 在文件夹中未找到图像文件: {folder_path}")
        print(f"支持的格式: {', '.join(image_extensions)}")
        return False

    print(f"📁 找到 {len(image_files)} 张图像文件")

    # 询问是否继续
    if len(image_files) > 10:
        confirm = input(f"⚠️ 将分析 {len(image_files)} 张图像，可能需要较长时间。是否继续？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 已取消批量分析")
            return False

    # 询问是否生成可视化图
    print(f"\n🎨 是否为所有图像生成可视化分析图？")
    generate_visuals = input("输入 y 生成，n 跳过: ").strip().lower()
    should_generate_visuals = generate_visuals in ['y', 'yes', '是']

    if should_generate_visuals:
        print("✅ 将为每张图像生成可视化分析图")
    else:
        print("⏭️ 跳过可视化生成，仅进行数据分析")

    # 批量分析
    results = []
    failed_files = []

    print(f"\n🔍 开始批量分析...")
    for i, image_file in enumerate(image_files, 1):
        print(f"📊 分析进度: {i}/{len(image_files)} - {image_file.name}")

        try:
            # 分析图像（禁用OCR）
            result = analyze_gas_meter_image(str(image_file))

            # 根据用户选择生成可视化
            visual_path = None
            if should_generate_visuals:
                visual_path = create_comprehensive_visual_analysis(str(image_file), result)

            results.append({
                'file_path': str(image_file),
                'file_name': image_file.name,
                'result': result,
                'visual_path': visual_path
            })

            print(f"   ✅ 完成")

        except Exception as e:
            print(f"   ❌ 失败: {str(e)}")
            failed_files.append({
                'file_path': str(image_file),
                'file_name': image_file.name,
                'error': str(e)
            })

    # 生成批量分析报告
    print(f"\n📊 批量分析完成！")
    print(f"✅ 成功分析: {len(results)} 张")
    print(f"❌ 分析失败: {len(failed_files)} 张")

    if results:
        # 显示分析结果摘要
        print(f"\n📋 分析结果摘要:")
        print(f"{'文件名':<40} {'综合得分':<10} {'质量等级':<10} {'OCR适用'}")
        print("-" * 70)

        for item in results:
            filename = item['file_name']
            summary = item['result']['summary']
            score = summary['overall_score']
            grade = summary['quality_grade']
            suitable = '✅' if summary['is_suitable_for_gas_meter'] else '❌'

            # 截断过长的文件名
            display_name = filename[:37] + "..." if len(filename) > 40 else filename
            print(f"{display_name:<40} {score:<10.3f} {grade:<10} {suitable}")

        # 统计信息
        scores = [item['result']['summary']['overall_score'] for item in results]
        suitable_count = sum(1 for item in results if item['result']['summary']['is_suitable_for_gas_meter'])

        print(f"\n📈 统计信息:")
        print(f"   平均得分: {np.mean(scores):.3f}")
        print(f"   最高得分: {max(scores):.3f}")
        print(f"   最低得分: {min(scores):.3f}")
        print(f"   OCR适用率: {suitable_count}/{len(results)} ({suitable_count/len(results)*100:.1f}%)")

        # 保存批量分析报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = Path("output_results") / f"batch_analysis_report_{timestamp}.json"

        # 准备JSON安全的结果数据
        json_safe_results = []
        for item in results:
            json_safe_results.append({
                'file_path': item['file_path'],
                'file_name': item['file_name'],
                'visual_path': item['visual_path'],
                'summary': {
                    'overall_score': float(item['result']['summary'].get('overall_score', 0)),
                    'quality_grade': str(item['result']['summary'].get('quality_grade', '')),
                    'is_suitable_for_gas_meter': bool(item['result']['summary'].get('is_suitable_for_gas_meter', False))
                }
            })

        batch_report = {
            "analysis_time": timestamp,
            "folder_path": str(folder_path),
            "total_files": len(image_files),
            "successful_analysis": len(results),
            "failed_analysis": len(failed_files),
            "statistics": {
                "average_score": float(np.mean(scores)),
                "max_score": float(max(scores)),
                "min_score": float(min(scores)),
                "ocr_suitable_count": int(suitable_count),
                "ocr_suitable_rate": float(suitable_count/len(results))
            },
            "results": json_safe_results,
            "failed_files": failed_files
        }

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(batch_report, f, ensure_ascii=False, indent=2)

        print(f"📁 详细报告已保存: {report_path}")

    if failed_files:
        print(f"\n❌ 失败文件列表:")
        for item in failed_files:
            print(f"   {item['file_name']}: {item['error']}")

    return True

def main():
    """主程序"""
    print("🎨 图像质量可视化分析工具")
    print("=" * 60)
    print("本工具可以生成类似您提供示例的图像质量分析界面")
    print("包含原始图像、中央区域、边缘检测、梯度分析、质量指标等")
    
    while True:
        print("\n请选择分析模式:")
        print("1. 单张图像质量分析")
        print("2. 两张图像对比分析")
        print("3. 文件夹批量分析")
        print("4. 退出程序")

        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == '1':
            single_image_analysis()
        elif choice == '2':
            comparison_analysis()
        elif choice == '3':
            folder_batch_analysis()
        elif choice == '4':
            print("\n👋 感谢使用图像质量分析工具！")
            break
        else:
            print("❌ 无效选择，请输入 1、2、3 或 4")

        # 询问是否继续
        if choice in ['1', '2', '3']:
            continue_choice = input("\n是否继续分析其他图像？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("\n👋 感谢使用图像质量分析工具！")
                break

if __name__ == "__main__":
    main()
