{"detector_config": {"center_crop_ratio": 0.8, "gradient_kernel_size": 3, "gradient_threshold": 20.0, "frequency_cutoff": 0.25, "frequency_threshold": 0.18, "motion_blur_threshold": 0.85, "directional_variance_ratio": 2.0, "brightness_low": 50, "brightness_high": 200, "contrast_threshold": 60, "weights": {"gradient_score": 0.5, "frequency_score": 0.35, "lighting_score": 0.15}}, "gas_meter_specific": {"min_resolution": {"width": 800, "height": 600}, "preferred_resolution": {"width": 1200, "height": 800}, "digit_area_focus": true, "ocr_readiness_check": true, "contrast_enhancement": true, "digit_clarity_threshold": 0.75}, "quality_thresholds": {"excellent": 0.85, "good": 0.75, "fair": 0.6, "poor": 0.0, "sharpness_requirements": {"basic_threshold": 0.75, "gradient_threshold": 0.7, "frequency_threshold": 0.65, "strong_gradient_ratio": 0.12}}, "explanation_settings": {"include_technical_details": true, "include_recommendations": true, "include_ocr_suggestions": true, "language": "chinese"}, "ocr_config": {"enabled": true, "min_confidence": 0.5, "min_text_area": 100, "roi_detection": {"min_contour_area": 500, "max_contour_area": 50000, "aspect_ratio_range": [0.1, 10.0]}, "text_clarity_weight": 0.15, "digit_patterns": ["\\d+\\.?\\d*", "[0-9]+", "\\d{4,}"], "preprocessing": {"min_roi_size": 32, "scale_factor": 2.0, "denoise_kernel": 3, "clahe_clip_limit": 2.0, "clahe_tile_size": 8}}}