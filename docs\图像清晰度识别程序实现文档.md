# 燃气表图像清晰度检测 - 参数调整指南

## 主要调整参数位置

### 1. 配置文件：燃气表检测配置.json

```json
{
  "detector_config": {
    "center_crop_ratio": 0.8,        // 中央区域提取比例
                                     // 0.8 = 只分析中央80%区域
                                     // 调大(0.9) = 分析更大范围，可能包含边缘噪声
                                     // 调小(0.6) = 只看最中心，更专注但可能漏掉边缘数字

    "contrast_threshold": 60,        // 对比度合格标准
                                     // 60 = 对比度必须达到60才算合格
                                     // 调低(40) = 要求不严格，更多图像通过
                                     // 调高(80) = 要求严格，只有高对比度图像通过

    "brightness_low": 50,            // 最暗亮度限制
                                     // 50 = 亮度低于50认为太暗
                                     // 调低(30) = 允许更暗的图像
                                     // 调高(70) = 不允许暗图像

    "brightness_high": 200,          // 最亮亮度限制
                                     // 200 = 亮度高于200认为太亮
                                     // 调低(180) = 不允许亮图像
                                     // 调高(230) = 允许更亮的图像

    "weights": {                     // 各项指标的重要性权重
      "gradient_score": 0.5,         // 梯度得分权重50%（边缘清晰度）
      "frequency_score": 0.35,       // 频率得分权重35%（细节丰富度）
      "lighting_score": 0.15         // 光照得分权重15%（亮度质量）
    }                                // 所有权重加起来必须等于1.0
  },

  "quality_thresholds": {            // 质量等级划分标准
    "excellent": 0.85,               // 优秀等级门槛
                                     // 0.85 = 得分85%以上才是优秀
                                     // 调低(0.80) = 更容易得优秀
                                     // 调高(0.90) = 很难得优秀

    "good": 0.75,                    // 良好等级门槛
                                     // 0.75 = 得分75%-85%是良好
                                     // 调低(0.65) = 更容易得良好
                                     // 调高(0.80) = 很难得良好

    "fair": 0.6                      // 一般等级门槛
                                     // 0.6 = 得分60%-75%是一般
                                     // 调低(0.50) = 更容易得一般
                                     // 调高(0.70) = 很难得一般
  },

  "gas_meter_specific": {            // 燃气表专用设置
    "digit_clarity_threshold": 0.75, // 数字清晰度要求
                                     // 0.75 = 数字清晰度必须达到75%
                                     // 调低(0.65) = 对数字清晰度要求降低
                                     // 调高(0.85) = 对数字清晰度要求提高

    "min_resolution": {              // 最低分辨率要求
      "width": 800,                  // 宽度至少800像素
      "height": 600                  // 高度至少600像素
    },

    "preferred_resolution": {        // 推荐分辨率
      "width": 1200,                 // 推荐宽度1200像素
      "height": 800                  // 推荐高度800像素
    }
  }
}
```

### 2. 代码文件：src/gas_meter_detector.py

**边缘检测参数（第202行）：**
```python
edges = cv2.Canny(digit_area, 50, 150)
# 50 = 低阈值（弱边缘阈值）- 调低检测更多细微边缘
# 150 = 高阈值（强边缘阈值）- 调低检测更多边缘
# 强边缘 = 数字轮廓线（黑白对比明显）
# 弱边缘 = 阴影、划痕（对比度不明显）
# 建议两个数字一起调，保持比例约1:3
```

**Sobel算子参数（第189-190行）：**
```python
sobel_x = cv2.Sobel(digit_area, cv2.CV_64F, 1, 0, ksize=3)
sobel_y = cv2.Sobel(digit_area, cv2.CV_64F, 0, 1, ksize=3)
# ksize=3 = 核大小
# 调大 = 更平滑，抗噪声
# 调小 = 更精细，检测细节
```

**评分权重（第226-233行）：**
```python
digit_clarity_score = (
    edge_score * 0.2 +                    # 20% - 局部边缘清晰度
    local_contrast_score * 0.15 +         # 15% - 数字区域对比度
    global_contrast_score * 0.15 +        # 15% - 整体图像对比度
    edge_density_score * 0.1 +            # 10% - 边缘密度（细节丰富度）
    global_edge_score * 0.15 +            # 15% - 全局边缘质量
    text_clarity_score * 0.25             # 25% - 文字清晰度（最重要）
)
# 所有权重加起来 = 1.0 (100%)
# 调大某个权重 = 该指标更重要，其他权重要相应调小
# 例如：更重视对比度，可以改为 local_contrast_score * 0.25
```

**亮度评分范围（第250-256行）：**
```python
if avg_brightness >= 100 and avg_brightness <= 180:
    brightness_quality = 1.2  # 给20%奖励分
elif avg_brightness >= 80 and avg_brightness <= 200:
    brightness_quality = 1.0  # 正常分数，不加分不扣分
else:
    brightness_quality = 0.8  # 扣20%分数
# 100-180 = 最佳亮度范围（数字最清楚）
# 80-200 = 可接受范围（还能识别）
# 其他 = 太暗或太亮（影响识别）
# 调整这些数值可以改变对亮度的要求严格程度
```

## 常见调整场景

### 场景1：程序说图像"不够清晰"，但看起来还可以
**解决方法：**
- 降低质量阈值：`"good": 0.65`
- 降低对比度要求：`"contrast_threshold": 40`
- 降低边缘检测阈值：`cv2.Canny(digit_area, 30, 120)`

### 场景2：程序对模糊图像评分过高
**解决方法：**
- 提高质量阈值：`"excellent": 0.90`
- 提高对比度要求：`"contrast_threshold": 80`
- 提高边缘检测阈值：`cv2.Canny(digit_area, 70, 180)`

### 场景3：光照不均匀的图像被误判
**解决方法：**
- 扩大亮度范围：`"brightness_low": 30, "brightness_high": 220`
- 调整理想亮度范围：`if avg_brightness >= 80 and avg_brightness <= 200:`

### 场景4：高分辨率图像检测效果不好
**解决方法：**
- 增大核大小：`ksize=5`
- 调整中央区域：`"center_crop_ratio": 0.9`

## 快速调整表

| 想要的效果 | 调整参数 | 调高/调低 | 具体数值建议 |
|-----------|---------|----------|-------------|
| 更容易通过检测 | quality_thresholds | 调低 | good: 0.65, excellent: 0.80 |
| 更严格的检测 | quality_thresholds | 调高 | good: 0.80, excellent: 0.90 |
| 接受更暗图像 | brightness_low | 调低 | 从50改为30 |
| 接受更亮图像 | brightness_high | 调高 | 从200改为230 |
| 检测更多边缘 | Canny阈值(50,150) | 调低 | 改为(30,120) |
| 减少噪声干扰 | Canny阈值(50,150) | 调高 | 改为(70,180) |
| 更重视对比度 | 权重 | 调高 | local_contrast_score * 0.25 |
| 更重视文字清晰度 | 权重 | 调高 | text_clarity_score * 0.35 |

**重要提醒：**
- 每次只调一个参数，测试效果后再调下一个
- 权重调整时，所有权重加起来必须等于1.0
- Canny的两个阈值建议一起调整，保持比例约1:3
