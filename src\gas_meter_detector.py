#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
燃气表专用图像质量检测器（集成OCR功能）
专门针对燃气表数字识别的图像质量检测，包含OCR文字识别和清晰度评估
"""

import cv2
import numpy as np
import json
from pathlib import Path
from typing import Dict, Tuple, Any, List
import logging
import re
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# OCR相关导入
try:
    import easyocr
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logger.warning("EasyOCR未安装，将使用模拟OCR功能进行演示。要启用真实OCR，请运行: pip install easyocr")

class GasMeterDetector:
    """燃气表专用图像质量检测器（集成OCR功能）"""

    def __init__(self, config_path: str = "燃气表检测配置.json"):
        """
        初始化燃气表检测器

        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)

        # 初始化OCR引擎
        self.ocr_reader = None
        self.use_mock_ocr = False

        if OCR_AVAILABLE and self.config.get("ocr_config", {}).get("enabled", True):
            try:
                logger.info("正在初始化EasyOCR引擎...")
                # 支持中文和英文
                self.ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
                logger.info("EasyOCR引擎初始化成功")
            except Exception as e:
                logger.warning(f"EasyOCR引擎初始化失败: {e}")
                self.ocr_reader = None
                self.use_mock_ocr = True
        else:
            logger.info("使用模拟OCR功能进行演示")
            self.use_mock_ocr = True
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"配置文件 {config_path} 不存在，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "detector_config": {
                "center_crop_ratio": 0.8,
                "gradient_threshold": 20.0,
                "frequency_threshold": 0.18,
                "contrast_threshold": 60,
                "weights": {
                    "gradient_score": 0.5,
                    "frequency_score": 0.35,
                    "lighting_score": 0.15
                }
            },
            "gas_meter_specific": {
                "min_resolution": {"width": 800, "height": 600},
                "preferred_resolution": {"width": 1200, "height": 800},
                "digit_clarity_threshold": 0.75
            },
            "quality_thresholds": {
                "excellent": 0.85,
                "good": 0.75,
                "fair": 0.6,
                "poor": 0.0
            },
            "ocr_config": {
                "enabled": True,
                "min_confidence": 0.5,
                "min_text_area": 100,
                "roi_detection": {
                    "min_contour_area": 500,
                    "max_contour_area": 50000,
                    "aspect_ratio_range": [0.1, 10.0]
                },
                "text_clarity_weight": 0.15,  # OCR文字清晰度在总评分中的权重
                "digit_patterns": [
                    r'\d+\.?\d*',  # 数字（可能包含小数点）
                    r'[0-9]+',     # 纯数字
                    r'\d{4,}',     # 4位以上数字（如表号）
                ]
            }
        }
    
    def check_resolution(self, image: np.ndarray) -> Dict[str, Any]:
        """检查图像分辨率是否符合燃气表检测要求"""
        height, width = image.shape[:2]
        
        min_res = self.config["gas_meter_specific"]["min_resolution"]
        preferred_res = self.config["gas_meter_specific"]["preferred_resolution"]
        
        # 分辨率评估
        meets_minimum = width >= min_res["width"] and height >= min_res["height"]
        meets_preferred = width >= preferred_res["width"] and height >= preferred_res["height"]
        
        # 计算分辨率得分
        # 对于燃气表检测，目标是至少1200x800，比它高的就可以
        width_ratio = width / preferred_res["width"]
        height_ratio = height / preferred_res["height"]

        # 如果两个维度都达到或超过目标分辨率，给满分
        if width >= preferred_res["width"] and height >= preferred_res["height"]:
            resolution_score = 1.0
        else:
            # 如果没有达到目标，按比例计算得分
            resolution_score = min(width_ratio, height_ratio)
            resolution_score = max(0.0, min(resolution_score, 1.0))
        
        return {
            "width": width,
            "height": height,
            "meets_minimum": meets_minimum,
            "meets_preferred": meets_preferred,
            "resolution_score": resolution_score,
            "recommendation": self._get_resolution_recommendation(width, height, meets_minimum, meets_preferred)
        }
    
    def _get_resolution_recommendation(self, width: int, height: int, meets_min: bool, meets_pref: bool) -> str:
        """获取分辨率建议"""
        if meets_pref:
            if width > 1200 or height > 800:
                return f"分辨率{width}×{height}优秀，超过目标要求1200×800，完全满足燃气表数字识别要求"
            else:
                return f"分辨率{width}×{height}优秀，达到目标要求1200×800，完全满足燃气表数字识别要求"
        elif meets_min:
            return f"分辨率{width}×{height}基本满足要求，建议提升至1200×800以获得更好效果"
        else:
            return f"分辨率{width}×{height}过低，不适合燃气表数字识别，建议至少800×600"
    
    def analyze_digit_area_clarity(self, image: np.ndarray) -> Dict[str, Any]:
        """分析数字区域清晰度 - 改进版本，重视全局质量和亮度均匀性"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # 提取中央区域（燃气表数字通常在中央）
        crop_ratio = self.config["detector_config"]["center_crop_ratio"]
        h, w = gray.shape
        center_h, center_w = int(h * crop_ratio), int(w * crop_ratio)
        start_h, start_w = (h - center_h) // 2, (w - center_w) // 2
        digit_area = gray[start_h:start_h+center_h, start_w:start_w+center_w]

        # 全局亮度均匀性分析
        global_brightness = np.mean(gray)
        brightness_std = np.std(gray)
        brightness_uniformity = 1.0 - min(brightness_std / global_brightness, 1.0) if global_brightness > 0 else 0

        # 分块亮度均匀性检查（检测光线不均匀）
        block_size = min(h//4, w//4, 100)
        brightness_blocks = []
        for i in range(0, h-block_size, block_size):
            for j in range(0, w-block_size, block_size):
                block = gray[i:i+block_size, j:j+block_size]
                brightness_blocks.append(np.mean(block))

        block_brightness_std = np.std(brightness_blocks) if brightness_blocks else 0
        lighting_uniformity = 1.0 - min(block_brightness_std / 50.0, 1.0)  # 50为经验阈值
        
        # 计算数字区域的清晰度指标
        # 1. 边缘清晰度（使用Sobel算子）
        sobel_x = cv2.Sobel(digit_area, cv2.CV_64F, 1, 0, ksize=3)
        sobel_y = cv2.Sobel(digit_area, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
        edge_clarity = np.mean(gradient_magnitude)

        # 2. 对比度分析（局部和全局）
        local_contrast = np.std(digit_area)
        global_contrast = np.std(gray)

        # 3. 局部方差（细节丰富度）
        local_variance = cv2.Laplacian(digit_area, cv2.CV_64F).var()

        # 4. 数字边缘检测
        edges = cv2.Canny(digit_area, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size

        # 5. 全局边缘检测（评估整体清晰度）
        global_edges = cv2.Canny(gray, 50, 150)
        global_edge_density = np.sum(global_edges > 0) / global_edges.size

        # 6. 文字区域清晰度（多区域采样）
        text_clarity_scores = []
        # 采样多个可能包含文字的区域
        sample_regions = [
            (0, 0, h//3, w//3),           # 左上
            (0, 2*w//3, h//3, w),         # 右上
            (2*h//3, 0, h, w//3),         # 左下
            (2*h//3, 2*w//3, h, w),       # 右下
        ]

        for y1, x1, y2, x2 in sample_regions:
            region = gray[y1:y2, x1:x2]
            if region.size > 0:
                region_edges = cv2.Canny(region, 30, 100)
                region_clarity = np.sum(region_edges > 0) / region_edges.size
                text_clarity_scores.append(region_clarity)

        text_clarity_avg = np.mean(text_clarity_scores) if text_clarity_scores else 0
        
        # 综合评分 - 全局质量优先版本
        # 基础清晰度得分
        edge_score = min(edge_clarity / 15.0, 1.0)  # 降低阈值
        local_contrast_score = min(local_contrast / 40.0, 1.0)  # 降低阈值
        global_contrast_score = min(global_contrast / 50.0, 1.0)
        edge_density_score = min(edge_density * 20, 1.0)  # 提高权重
        global_edge_score = min(global_edge_density * 25, 1.0)  # 全局边缘得分
        text_clarity_score = min(text_clarity_avg * 30, 1.0)  # 文字清晰度得分

        # 亮度质量评估
        avg_brightness = np.mean(digit_area)
        brightness_quality = 1.0
        if avg_brightness >= 100 and avg_brightness <= 180:
            # 理想亮度范围，给予奖励
            brightness_quality = 1.2  # 20%奖励
        elif avg_brightness >= 80 and avg_brightness <= 200:
            # 可接受范围
            brightness_quality = 1.0
        else:
            # 过暗或过亮
            brightness_quality = 0.8

        # 光线均匀性奖励
        uniformity_bonus = (brightness_uniformity * 0.3 + lighting_uniformity * 0.7)

        # 综合评分：重视全局质量、亮度均匀性和文字清晰度
        digit_clarity_score = (
            edge_score * 0.2 +                    # 局部边缘
            local_contrast_score * 0.15 +         # 局部对比度
            global_contrast_score * 0.15 +        # 全局对比度
            edge_density_score * 0.1 +            # 局部边缘密度
            global_edge_score * 0.15 +            # 全局边缘密度
            text_clarity_score * 0.25             # 文字清晰度
        )

        # 应用亮度质量和均匀性调整
        digit_clarity_score *= brightness_quality
        digit_clarity_score += uniformity_bonus * 0.2  # 均匀性奖励

        # 确保得分在合理范围内
        digit_clarity_score = min(digit_clarity_score, 1.0)
        
        return {
            "digit_clarity_score": digit_clarity_score,
            "edge_clarity": edge_clarity,
            "contrast": local_contrast,
            "global_contrast": global_contrast,
            "local_variance": local_variance,
            "edge_density": edge_density,
            "global_edge_density": global_edge_density,
            "text_clarity": text_clarity_avg,
            "brightness_uniformity": brightness_uniformity,
            "lighting_uniformity": lighting_uniformity,
            "brightness_quality": brightness_quality,
            "is_digit_readable": digit_clarity_score >= self.config["gas_meter_specific"]["digit_clarity_threshold"],
            "explanation": self._explain_digit_clarity(digit_clarity_score, local_contrast, edge_clarity,
                                                     brightness_uniformity, lighting_uniformity)
        }
    
    def _explain_digit_clarity(self, score: float, contrast: float, edge_clarity: float,
                              brightness_uniformity: float = 0, lighting_uniformity: float = 0) -> str:
        """解释数字清晰度"""
        explanation_parts = []

        if score >= 0.85:
            explanation_parts.append(f"数字区域非常清晰（得分{score:.3f}）")
        elif score >= 0.75:
            explanation_parts.append(f"数字区域清晰度良好（得分{score:.3f}）")
        elif score >= 0.6:
            explanation_parts.append(f"数字区域清晰度一般（得分{score:.3f}）")
        elif score >= 0.4:
            explanation_parts.append(f"数字区域较模糊（得分{score:.3f}）")
        else:
            explanation_parts.append(f"数字区域模糊严重（得分{score:.3f}）")

        # 添加详细分析
        explanation_parts.append(f"对比度{contrast:.1f}")
        explanation_parts.append(f"边缘锐利度{edge_clarity:.1f}")

        if brightness_uniformity > 0:
            if brightness_uniformity >= 0.8:
                explanation_parts.append("亮度分布均匀")
            elif brightness_uniformity >= 0.6:
                explanation_parts.append("亮度分布较均匀")
            else:
                explanation_parts.append("亮度分布不均")

        if lighting_uniformity > 0:
            if lighting_uniformity >= 0.8:
                explanation_parts.append("光线分布均匀")
            elif lighting_uniformity >= 0.6:
                explanation_parts.append("光线分布较均匀")
            else:
                explanation_parts.append("光线分布不均")

        # 最终评估
        if score >= 0.75:
            explanation_parts.append("适合OCR识别")
        elif score >= 0.6:
            explanation_parts.append("OCR识别可能有困难")
        else:
            explanation_parts.append("不适合OCR识别")

        return "，".join(explanation_parts)
    
    def check_ocr_readiness(self, image: np.ndarray) -> Dict[str, Any]:
        """检查图像是否适合OCR数字识别（集成OCR功能）"""
        # 分辨率检查
        resolution_result = self.check_resolution(image)

        # 数字区域清晰度检查
        digit_result = self.analyze_digit_area_clarity(image)

        # 光线条件检查
        lighting_result = self._analyze_lighting_for_ocr(image)

        # OCR文字识别和清晰度评估
        ocr_text_result = self.perform_comprehensive_ocr_analysis(image)

        # 综合OCR适用性评分
        # 如果有OCR文字识别结果，将其纳入评分
        if ocr_text_result["text_clarity_available"]:
            text_clarity_weight = self.config["ocr_config"]["text_clarity_weight"]
            # 调整权重以包含文字清晰度
            base_weight = 1.0 - text_clarity_weight
            ocr_score = (
                resolution_result["resolution_score"] * 0.3 * base_weight +
                digit_result["digit_clarity_score"] * 0.5 * base_weight +
                lighting_result["lighting_score"] * 0.2 * base_weight +
                ocr_text_result["text_clarity_score"] * text_clarity_weight
            )
        else:
            # 没有OCR结果时使用原有评分机制
            ocr_score = (
                resolution_result["resolution_score"] * 0.3 +
                digit_result["digit_clarity_score"] * 0.5 +
                lighting_result["lighting_score"] * 0.2
            )

        # OCR适用性判断
        basic_ocr_ready = (
            resolution_result["meets_minimum"] and
            digit_result["is_digit_readable"] and
            lighting_result["is_suitable_for_ocr"] and
            ocr_score >= 0.7
        )

        # 如果有文字识别结果，考虑文字质量
        if ocr_text_result["text_clarity_available"]:
            text_quality_ok = ocr_text_result["text_clarity_score"] >= 0.5
            ocr_ready = basic_ocr_ready and text_quality_ok
        else:
            ocr_ready = basic_ocr_ready

        return {
            "ocr_ready": ocr_ready,
            "ocr_score": ocr_score,
            "resolution_check": resolution_result,
            "digit_clarity_check": digit_result,
            "lighting_check": lighting_result,
            "ocr_text_analysis": ocr_text_result,
            "overall_recommendation": self._get_ocr_recommendation(ocr_ready, ocr_score, ocr_text_result)
        }

    def perform_comprehensive_ocr_analysis(self, image: np.ndarray) -> Dict[str, Any]:
        """执行综合OCR分析"""
        try:
            # 1. 检测文字ROI区域
            rois = self.detect_text_rois(image)

            # 2. 对ROI区域执行OCR识别
            ocr_results = self.perform_ocr_on_rois(image, rois)

            # 3. 评估文字清晰度
            text_clarity_result = self.evaluate_text_clarity(image, ocr_results)

            # 合并结果
            comprehensive_result = {
                **text_clarity_result,
                "roi_detection": {
                    "total_rois": len(rois),
                    "roi_details": rois
                },
                "ocr_recognition": ocr_results
            }

            return comprehensive_result

        except Exception as e:
            logger.error(f"OCR综合分析失败: {e}")
            return {
                "text_clarity_available": False,
                "text_clarity_score": 0.0,
                "error": str(e),
                "roi_detection": {"total_rois": 0, "roi_details": []},
                "ocr_recognition": {"ocr_available": False, "error": str(e)}
            }
    
    def _analyze_lighting_for_ocr(self, image: np.ndarray) -> Dict[str, Any]:
        """分析光线条件是否适合OCR"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 亮度分析
        brightness = np.mean(gray)
        
        # 对比度分析
        contrast = np.std(gray)
        
        # 判断是否适合OCR
        brightness_ok = 80 <= brightness <= 180  # OCR适宜的亮度范围
        contrast_ok = contrast >= self.config["detector_config"]["contrast_threshold"]
        
        # 光线评分 - 修正版本
        # 使用更合理的亮度评分曲线，不过度惩罚高亮度
        if brightness >= 80 and brightness <= 180:
            # 在合理范围内，给予高分
            if brightness >= 120 and brightness <= 160:
                brightness_score = 1.0  # 理想范围
            else:
                # 在合理范围边缘，轻微降分
                if brightness < 120:
                    brightness_score = 0.8 + (brightness - 80) / 40 * 0.2  # 80-120: 0.8-1.0
                else:
                    brightness_score = 1.0 - (brightness - 160) / 20 * 0.2  # 160-180: 1.0-0.8
        else:
            # 超出合理范围，按距离惩罚
            if brightness < 80:
                brightness_score = max(0.3, brightness / 80 * 0.8)
            else:  # brightness > 180
                brightness_score = max(0.5, 1.0 - (brightness - 180) / 100 * 0.5)

        # 对比度评分 - 降低阈值，更合理评估
        contrast_score = min(contrast / 60.0, 1.0)  # 降低从100到60

        lighting_score = (brightness_score * 0.6 + contrast_score * 0.4)
        
        return {
            "brightness": brightness,
            "contrast": contrast,
            "brightness_ok": brightness_ok,
            "contrast_ok": contrast_ok,
            "lighting_score": lighting_score,
            "is_suitable_for_ocr": brightness_ok and contrast_ok,
            "lighting_advice": self._get_lighting_advice(brightness, contrast, brightness_ok, contrast_ok)
        }

    def detect_text_rois(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        检测图像中可能包含文字的ROI区域

        Args:
            image: 输入图像

        Returns:
            ROI区域列表，每个区域包含坐标和特征信息
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        h, w = gray.shape

        rois = []

        # 方法1: 基于轮廓检测的ROI
        # 使用形态学操作增强文字区域
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        morph = cv2.morphologyEx(gray, cv2.MORPH_GRADIENT, kernel)

        # 二值化
        _, binary = cv2.threshold(morph, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 水平和垂直结构元素，用于连接文字
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))

        # 检测水平和垂直线条
        horizontal = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel)
        vertical = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)

        # 合并水平和垂直特征
        combined = cv2.addWeighted(horizontal, 0.5, vertical, 0.5, 0)

        # 膨胀操作连接相邻的文字区域
        dilate_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        dilated = cv2.dilate(combined, dilate_kernel, iterations=2)

        # 查找轮廓
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        roi_config = self.config["ocr_config"]["roi_detection"]

        for i, contour in enumerate(contours):
            area = cv2.contourArea(contour)

            # 过滤太小或太大的区域
            if area < roi_config["min_contour_area"] or area > roi_config["max_contour_area"]:
                continue

            # 获取边界框
            x, y, w_box, h_box = cv2.boundingRect(contour)

            # 检查宽高比
            aspect_ratio = w_box / h_box if h_box > 0 else 0
            if not (roi_config["aspect_ratio_range"][0] <= aspect_ratio <= roi_config["aspect_ratio_range"][1]):
                continue

            # 确保ROI在图像范围内
            x = max(0, x)
            y = max(0, y)
            w_box = min(w_box, w - x)
            h_box = min(h_box, h - y)

            if w_box < 20 or h_box < 10:  # 最小尺寸检查
                continue

            # 提取ROI区域
            roi_region = gray[y:y+h_box, x:x+w_box]

            # 计算ROI特征
            roi_mean = np.mean(roi_region)
            roi_std = np.std(roi_region)

            # 边缘密度
            edges = cv2.Canny(roi_region, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size

            rois.append({
                "id": i,
                "bbox": (x, y, w_box, h_box),
                "area": area,
                "aspect_ratio": aspect_ratio,
                "mean_intensity": roi_mean,
                "std_intensity": roi_std,
                "edge_density": edge_density,
                "roi_type": "contour_based"
            })

        # 方法2: 预定义的燃气表关键区域
        # 数字显示区域（通常在中央）
        center_x, center_y = w // 2, h // 2
        digit_w, digit_h = min(w // 3, 300), min(h // 4, 100)
        digit_roi = {
            "id": len(rois),
            "bbox": (center_x - digit_w//2, center_y - digit_h//2, digit_w, digit_h),
            "area": digit_w * digit_h,
            "aspect_ratio": digit_w / digit_h,
            "roi_type": "predefined_digit_area"
        }

        # 计算预定义区域的特征
        x, y, w_box, h_box = digit_roi["bbox"]
        if 0 <= x < w and 0 <= y < h and x + w_box <= w and y + h_box <= h:
            roi_region = gray[y:y+h_box, x:x+w_box]
            digit_roi["mean_intensity"] = np.mean(roi_region)
            digit_roi["std_intensity"] = np.std(roi_region)
            edges = cv2.Canny(roi_region, 50, 150)
            digit_roi["edge_density"] = np.sum(edges > 0) / edges.size
            rois.append(digit_roi)

        # 标签区域（通常在上方或下方）
        label_regions = [
            (w//4, h//8, w//2, h//6),      # 上方标签区域
            (w//4, 7*h//8, w//2, h//8),    # 下方标签区域
            (w//8, h//4, w//4, h//2),      # 左侧标签区域
            (5*w//8, h//4, w//4, h//2),    # 右侧标签区域
        ]

        for i, (x, y, w_box, h_box) in enumerate(label_regions):
            if x + w_box <= w and y + h_box <= h:
                roi_region = gray[y:y+h_box, x:x+w_box]
                label_roi = {
                    "id": len(rois),
                    "bbox": (x, y, w_box, h_box),
                    "area": w_box * h_box,
                    "aspect_ratio": w_box / h_box,
                    "mean_intensity": np.mean(roi_region),
                    "std_intensity": np.std(roi_region),
                    "roi_type": f"predefined_label_{i}"
                }
                edges = cv2.Canny(roi_region, 50, 150)
                label_roi["edge_density"] = np.sum(edges > 0) / edges.size
                rois.append(label_roi)

        logger.info(f"检测到 {len(rois)} 个潜在文字区域")
        return rois

    def perform_ocr_on_rois(self, image: np.ndarray, rois: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        对ROI区域执行OCR文字识别

        Args:
            image: 输入图像
            rois: ROI区域列表

        Returns:
            OCR识别结果
        """
        if self.use_mock_ocr:
            return self._mock_ocr_recognition(image, rois)

        if not self.ocr_reader:
            return {
                "ocr_available": False,
                "total_rois": len(rois),
                "successful_recognitions": 0,
                "text_results": [],
                "overall_confidence": 0.0,
                "error": "OCR引擎未初始化"
            }

        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        text_results = []
        successful_recognitions = 0
        total_confidence = 0.0

        min_confidence = self.config["ocr_config"]["min_confidence"]
        digit_patterns = self.config["ocr_config"]["digit_patterns"]

        for roi in rois:
            x, y, w, h = roi["bbox"]

            # 提取ROI区域
            roi_image = gray[y:y+h, x:x+w]

            # 预处理ROI图像以提高OCR准确性
            processed_roi = self._preprocess_roi_for_ocr(roi_image)

            try:
                # 执行OCR识别
                start_time = time.time()
                ocr_results = self.ocr_reader.readtext(processed_roi)
                ocr_time = time.time() - start_time

                roi_texts = []
                roi_confidences = []

                for (bbox_ocr, text, confidence) in ocr_results:
                    if confidence >= min_confidence and len(text.strip()) > 0:
                        # 检查是否匹配数字模式
                        is_digit = any(re.search(pattern, text) for pattern in digit_patterns)

                        roi_texts.append({
                            "text": text.strip(),
                            "confidence": confidence,
                            "is_digit": is_digit,
                            "bbox_in_roi": bbox_ocr
                        })
                        roi_confidences.append(confidence)

                if roi_texts:
                    successful_recognitions += 1
                    avg_confidence = np.mean(roi_confidences)
                    total_confidence += avg_confidence

                    text_results.append({
                        "roi_id": roi["id"],
                        "roi_bbox": roi["bbox"],
                        "roi_type": roi["roi_type"],
                        "texts": roi_texts,
                        "avg_confidence": avg_confidence,
                        "text_count": len(roi_texts),
                        "ocr_time": ocr_time,
                        "roi_features": {
                            "area": roi["area"],
                            "aspect_ratio": roi["aspect_ratio"],
                            "edge_density": roi.get("edge_density", 0)
                        }
                    })

            except Exception as e:
                logger.warning(f"ROI {roi['id']} OCR识别失败: {e}")
                continue

        overall_confidence = total_confidence / successful_recognitions if successful_recognitions > 0 else 0.0

        return {
            "ocr_available": True,
            "total_rois": len(rois),
            "successful_recognitions": successful_recognitions,
            "text_results": text_results,
            "overall_confidence": overall_confidence,
            "recognition_rate": successful_recognitions / len(rois) if len(rois) > 0 else 0.0
        }

    def _preprocess_roi_for_ocr(self, roi_image: np.ndarray) -> np.ndarray:
        """
        预处理ROI图像以提高OCR准确性

        Args:
            roi_image: ROI区域图像

        Returns:
            预处理后的图像
        """
        # 1. 调整大小（如果太小）
        h, w = roi_image.shape
        if h < 32 or w < 32:
            scale_factor = max(32/h, 32/w, 2.0)
            new_h, new_w = int(h * scale_factor), int(w * scale_factor)
            roi_image = cv2.resize(roi_image, (new_w, new_h), interpolation=cv2.INTER_CUBIC)

        # 2. 去噪
        denoised = cv2.medianBlur(roi_image, 3)

        # 3. 对比度增强
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)

        # 4. 锐化
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)

        # 5. 二值化（自适应阈值）
        binary = cv2.adaptiveThreshold(
            sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        return binary

    def evaluate_text_clarity(self, image: np.ndarray, ocr_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估文字清晰度

        Args:
            image: 输入图像
            ocr_results: OCR识别结果

        Returns:
            文字清晰度评估结果
        """
        if not ocr_results["ocr_available"] or ocr_results["successful_recognitions"] == 0:
            return {
                "text_clarity_available": False,
                "text_clarity_score": 0.0,
                "clarity_details": [],
                "average_confidence": 0.0,
                "text_quality_grade": "无文字",
                "explanation": "未检测到可识别的文字内容"
            }

        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        clarity_details = []
        total_weighted_score = 0.0
        total_weight = 0.0

        for text_result in ocr_results["text_results"]:
            roi_bbox = text_result["roi_bbox"]
            x, y, w, h = roi_bbox

            # 提取文字区域
            text_region = gray[y:y+h, x:x+w]

            # 1. OCR置信度得分（权重40%）
            confidence_score = text_result["avg_confidence"]

            # 2. 文字区域对比度得分（权重25%）
            contrast_score = min(np.std(text_region) / 60.0, 1.0)

            # 3. 边缘锐利度得分（权重20%）
            edges = cv2.Canny(text_region, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            sharpness_score = min(edge_density * 15, 1.0)

            # 4. 字符完整性得分（权重15%）
            # 基于文字数量和长度
            text_count = text_result["text_count"]
            total_chars = sum(len(t["text"]) for t in text_result["texts"])
            completeness_score = min((total_chars * text_count) / 20.0, 1.0)

            # 5. 数字识别奖励
            digit_bonus = 0.0
            digit_count = sum(1 for t in text_result["texts"] if t["is_digit"])
            if digit_count > 0:
                digit_bonus = min(digit_count * 0.1, 0.2)  # 最多20%奖励

            # 综合评分
            clarity_score = (
                confidence_score * 0.4 +
                contrast_score * 0.25 +
                sharpness_score * 0.2 +
                completeness_score * 0.15 +
                digit_bonus
            )

            # 区域权重（基于面积和类型）
            roi_features = text_result.get("roi_features", {})
            area = roi_features.get("area", 5000)  # 默认面积
            area_weight = min(area / 10000.0, 1.0)
            type_weight = 1.5 if "digit" in text_result["roi_type"] else 1.0
            region_weight = area_weight * type_weight

            clarity_details.append({
                "roi_id": text_result["roi_id"],
                "roi_type": text_result["roi_type"],
                "texts": [t["text"] for t in text_result["texts"]],
                "confidence_score": confidence_score,
                "contrast_score": contrast_score,
                "sharpness_score": sharpness_score,
                "completeness_score": completeness_score,
                "digit_bonus": digit_bonus,
                "clarity_score": clarity_score,
                "region_weight": region_weight,
                "text_count": text_count,
                "total_chars": total_chars,
                "has_digits": digit_count > 0
            })

            total_weighted_score += clarity_score * region_weight
            total_weight += region_weight

        # 计算加权平均清晰度得分
        text_clarity_score = total_weighted_score / total_weight if total_weight > 0 else 0.0
        text_clarity_score = min(text_clarity_score, 1.0)

        # 质量等级
        if text_clarity_score >= 0.8:
            quality_grade = "优秀"
        elif text_clarity_score >= 0.65:
            quality_grade = "良好"
        elif text_clarity_score >= 0.5:
            quality_grade = "一般"
        else:
            quality_grade = "较差"

        # 生成解释
        explanation = self._generate_text_clarity_explanation(
            text_clarity_score, clarity_details, ocr_results
        )

        return {
            "text_clarity_available": True,
            "text_clarity_score": text_clarity_score,
            "clarity_details": clarity_details,
            "average_confidence": ocr_results["overall_confidence"],
            "text_quality_grade": quality_grade,
            "total_text_regions": len(clarity_details),
            "digit_regions_count": sum(1 for d in clarity_details if d["has_digits"]),
            "explanation": explanation
        }

    def _generate_text_clarity_explanation(self, score: float, details: List[Dict], ocr_results: Dict) -> str:
        """生成文字清晰度解释"""
        explanations = []

        if score >= 0.8:
            explanations.append("文字清晰度优秀，OCR识别效果很好")
        elif score >= 0.65:
            explanations.append("文字清晰度良好，适合OCR识别")
        elif score >= 0.5:
            explanations.append("文字清晰度一般，OCR识别可能需要预处理")
        else:
            explanations.append("文字清晰度较差，建议重新拍摄")

        # 统计信息
        total_regions = len(details)
        digit_regions = sum(1 for d in details if d["has_digits"])
        avg_confidence = ocr_results["overall_confidence"]

        explanations.append(f"检测到{total_regions}个文字区域，其中{digit_regions}个包含数字")
        explanations.append(f"平均OCR置信度: {avg_confidence:.2f}")

        # 具体建议
        if avg_confidence < 0.6:
            explanations.append("建议改善光线条件或调整拍摄角度")

        low_clarity_count = sum(1 for d in details if d["clarity_score"] < 0.5)
        if low_clarity_count > 0:
            explanations.append(f"有{low_clarity_count}个区域清晰度不足")

        return "；".join(explanations)
    
    def _get_lighting_advice(self, brightness: float, contrast: float, brightness_ok: bool, contrast_ok: bool) -> str:
        """获取光线建议"""
        advice = []
        
        if not brightness_ok:
            if brightness < 80:
                advice.append("光线过暗，建议增加照明")
            else:
                advice.append("光线过亮，建议减少光源或调整角度")
        
        if not contrast_ok:
            advice.append(f"对比度{contrast:.1f}偏低，建议调整光线角度或使用补光")
        
        if brightness_ok and contrast_ok:
            advice.append(f"光线条件良好（亮度{brightness:.1f}，对比度{contrast:.1f}），适合数字识别")
        
        return "；".join(advice)
    
    def _get_ocr_recommendation(self, ocr_ready: bool, ocr_score: float, ocr_text_result: Dict[str, Any] = None) -> str:
        """获取OCR建议（包含文字识别信息）"""
        recommendations = []

        # 基础质量评估
        if ocr_ready:
            recommendations.append(f"图像质量优秀（OCR得分{ocr_score:.3f}），完全适合燃气表数字识别")
        elif ocr_score >= 0.6:
            recommendations.append(f"图像质量一般（OCR得分{ocr_score:.3f}），可尝试OCR识别，但可能需要预处理")
        else:
            recommendations.append(f"图像质量不佳（OCR得分{ocr_score:.3f}），不建议进行OCR识别，建议重新拍摄")

        # 添加OCR文字识别信息
        if ocr_text_result and ocr_text_result.get("text_clarity_available", False):
            text_regions = ocr_text_result.get("total_text_regions", 0)
            digit_regions = ocr_text_result.get("digit_regions_count", 0)
            text_score = ocr_text_result.get("text_clarity_score", 0)

            if text_regions > 0:
                recommendations.append(f"检测到{text_regions}个文字区域，其中{digit_regions}个包含数字")
                recommendations.append(f"文字清晰度{ocr_text_result.get('text_quality_grade', '未知')}（得分{text_score:.3f}）")

                if text_score < 0.5:
                    recommendations.append("建议改善光线条件或重新对焦以提高文字清晰度")
            else:
                recommendations.append("未检测到清晰的文字内容，可能需要调整拍摄角度或光线")
        elif ocr_text_result and "error" in ocr_text_result:
            recommendations.append("OCR文字识别功能暂时不可用")

        return "；".join(recommendations)
    
    def comprehensive_gas_meter_analysis(self, image: np.ndarray) -> Dict[str, Any]:
        """燃气表图像综合分析"""
        try:
            # OCR适用性检查
            ocr_result = self.check_ocr_readiness(image)
            
            # 生成详细报告
            report = {
                "analysis_type": "燃气表专用检测",
                "ocr_analysis": ocr_result,
                "summary": {
                    "is_suitable_for_gas_meter": ocr_result["ocr_ready"],
                    "overall_score": ocr_result["ocr_score"],
                    "quality_grade": self._get_quality_grade(ocr_result["ocr_score"]),
                    "main_issues": self._identify_main_issues(ocr_result),
                    "recommendations": self._generate_recommendations(ocr_result)
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"燃气表图像分析失败: {str(e)}")
            raise
    
    def _get_quality_grade(self, score: float) -> str:
        """获取质量等级"""
        thresholds = self.config["quality_thresholds"]
        if score >= thresholds["excellent"]:
            return "优秀"
        elif score >= thresholds["good"]:
            return "良好"
        elif score >= thresholds["fair"]:
            return "一般"
        else:
            return "较差"
    
    def _identify_main_issues(self, ocr_result: Dict) -> list:
        """识别主要问题（包含OCR文字识别问题）"""
        issues = []

        if not ocr_result["resolution_check"]["meets_minimum"]:
            issues.append("分辨率过低")

        if not ocr_result["digit_clarity_check"]["is_digit_readable"]:
            issues.append("数字区域模糊")

        if not ocr_result["lighting_check"]["brightness_ok"]:
            issues.append("光线亮度不适宜")

        if not ocr_result["lighting_check"]["contrast_ok"]:
            issues.append("对比度不足")

        # OCR文字识别相关问题
        ocr_text_analysis = ocr_result.get("ocr_text_analysis", {})
        if ocr_text_analysis.get("text_clarity_available", False):
            text_score = ocr_text_analysis.get("text_clarity_score", 0)
            total_regions = ocr_text_analysis.get("total_text_regions", 0)

            if total_regions == 0:
                issues.append("未检测到文字区域")
            elif text_score < 0.5:
                issues.append("文字清晰度不足")
            elif ocr_text_analysis.get("digit_regions_count", 0) == 0:
                issues.append("未检测到数字文字")
        elif "error" in ocr_text_analysis:
            issues.append("OCR功能异常")

        return issues if issues else ["无明显问题"]
    
    def _generate_recommendations(self, ocr_result: Dict) -> list:
        """生成改进建议（包含OCR文字识别建议）"""
        recommendations = []

        # 分辨率建议
        if not ocr_result["resolution_check"]["meets_preferred"]:
            recommendations.append("建议使用1200×800分辨率拍摄")

        # 清晰度建议
        if ocr_result["digit_clarity_check"]["digit_clarity_score"] < 0.75:
            recommendations.append("建议重新对焦，确保数字区域清晰")

        # 光线建议
        lighting_advice = ocr_result["lighting_check"]["lighting_advice"]
        if "建议" in lighting_advice:
            recommendations.append(lighting_advice)

        # OCR文字识别建议
        ocr_text_analysis = ocr_result.get("ocr_text_analysis", {})
        if ocr_text_analysis.get("text_clarity_available", False):
            text_score = ocr_text_analysis.get("text_clarity_score", 0)
            total_regions = ocr_text_analysis.get("total_text_regions", 0)
            digit_regions = ocr_text_analysis.get("digit_regions_count", 0)
            avg_confidence = ocr_text_analysis.get("average_confidence", 0)

            if total_regions == 0:
                recommendations.append("建议调整拍摄角度，确保燃气表文字区域清晰可见")
            elif text_score < 0.5:
                recommendations.append("建议改善光线条件，提高文字清晰度")
            elif digit_regions == 0:
                recommendations.append("建议确保数字显示区域在拍摄范围内且清晰可读")
            elif avg_confidence < 0.6:
                recommendations.append("建议重新拍摄，提高OCR识别置信度")

        # 通用建议
        if ocr_result["ocr_score"] < 0.7:
            recommendations.append("建议重新拍摄，确保燃气表数字清晰可读")

        return recommendations if recommendations else ["图像质量良好，无需改进"]

    def _mock_ocr_recognition(self, image: np.ndarray, rois: List[Dict]) -> Dict[str, Any]:
        """模拟OCR识别功能（用于演示）"""
        import random

        # 模拟的燃气表文字内容
        mock_texts = [
            "12345.67", "燃气表", "G1.6", "立方米", "m³",
            "2024", "检定", "有效期", "序列号", "SN123456",
            "压力", "kPa", "温度", "℃", "流量", "00123.45",
            "98765.43", "表号", "用户编号", "检定日期"
        ]

        text_results = []
        successful_recognitions = 0
        total_confidence = 0.0

        for roi in rois:
            # 根据ROI类型模拟不同的识别结果
            roi_type = roi.get("roi_type", "unknown")
            roi_texts = []

            # 模拟识别成功率（70-90%）
            if random.random() < 0.8:
                # 根据ROI类型选择合适的文字
                if "digit" in roi_type.lower():
                    # 数字显示区域更可能识别到数字
                    possible_texts = ["12345.67", "00123.45", "98765.43", "00000.12", "54321.98"]
                elif "label" in roi_type.lower():
                    # 标签区域更可能识别到标签文字
                    possible_texts = ["燃气表", "G1.6", "立方米", "m³", "检定", "有效期", "表号"]
                else:
                    possible_texts = mock_texts

                # 随机选择1-2个文字
                num_texts = random.randint(1, min(2, len(possible_texts)))
                selected_texts = random.sample(possible_texts, num_texts)

                for text in selected_texts:
                    # 模拟置信度（0.5-0.95）
                    confidence = random.uniform(0.5, 0.95)

                    # 数字文字通常置信度更高
                    if any(char.isdigit() for char in text):
                        confidence = random.uniform(0.7, 0.95)

                    # 检查是否匹配数字模式
                    digit_patterns = self.config["ocr_config"]["digit_patterns"]
                    is_digit = any(re.search(pattern, text) for pattern in digit_patterns)

                    roi_texts.append({
                        "text": text,
                        "confidence": confidence,
                        "is_digit": is_digit,
                        "bbox_in_roi": [[0, 0], [50, 0], [50, 20], [0, 20]]  # 模拟边框
                    })

            if roi_texts:
                successful_recognitions += 1
                roi_confidences = [t["confidence"] for t in roi_texts]
                avg_confidence = np.mean(roi_confidences)
                total_confidence += avg_confidence

                text_results.append({
                    "roi_id": roi["id"],
                    "roi_type": roi.get("roi_type", "unknown"),
                    "roi_bbox": roi["bbox"],
                    "texts": roi_texts,
                    "text_count": len(roi_texts),
                    "avg_confidence": avg_confidence,
                    "digit_count": sum(1 for t in roi_texts if t["is_digit"]),
                    "processing_time": random.uniform(0.1, 0.3),  # 模拟处理时间
                    "roi_features": {
                        "area": roi.get("area", 5000),
                        "aspect_ratio": roi.get("aspect_ratio", 2.0),
                        "edge_density": roi.get("edge_density", 0.1)
                    }
                })

        overall_confidence = total_confidence / successful_recognitions if successful_recognitions > 0 else 0.0

        return {
            "ocr_available": True,
            "total_rois": len(rois),
            "successful_recognitions": successful_recognitions,
            "text_results": text_results,
            "overall_confidence": overall_confidence,
            "mock_mode": True,  # 标识这是模拟结果
            "total_processing_time": sum(tr.get("processing_time", 0) for tr in text_results)
        }


def analyze_gas_meter_image(image_path: str, config_path: str = "燃气表检测配置.json", enable_ocr: bool = False) -> Dict[str, Any]:
    """
    分析单张燃气表图像

    Args:
        image_path: 图像路径
        config_path: 配置文件路径
        enable_ocr: 是否启用OCR功能（默认False）

    Returns:
        分析结果字典
    """
    detector = GasMeterDetector(config_path)

    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图像: {image_path}")

    # 执行分析
    result = detector.comprehensive_gas_meter_analysis(image)

    # 添加文件信息
    height, width = image.shape[:2]
    result["file_info"] = {
        "file_path": image_path,
        "file_name": Path(image_path).name,
        "file_size_mb": round(Path(image_path).stat().st_size / (1024 * 1024), 2),
        "width": width,
        "height": height
    }

    return result


if __name__ == "__main__":
    # 测试代码
    import sys

    if len(sys.argv) > 1:
        image_path = sys.argv[1]
        try:
            result = analyze_gas_meter_image(image_path)
            print("=" * 60)
            print("🔍 燃气表图像质量检测结果")
            print("=" * 60)
            print(f"文件名: {result['file_info']['file_name']}")
            print(f"文件大小: {result['file_info']['file_size_mb']} MB")
            print(f"OCR适用性: {'✅ 适合' if result['summary']['is_suitable_for_gas_meter'] else '❌ 不适合'}")
            print(f"综合得分: {result['summary']['overall_score']:.3f}")
            print(f"质量等级: {result['summary']['quality_grade']}")
            print(f"主要问题: {', '.join(result['summary']['main_issues'])}")
            print("改进建议:")
            for rec in result['summary']['recommendations']:
                print(f"  - {rec}")
        except Exception as e:
            print(f"分析失败: {e}")
    else:
        print("使用方法: python gas_meter_detector.py <图像路径>")
