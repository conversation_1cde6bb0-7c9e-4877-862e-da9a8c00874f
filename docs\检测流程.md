# 燃气表图像清晰度检测流程

## 程序检测图片的完整步骤

### 第1步：提取重点区域
- 只看图片中央80%的部分（燃气表数字通常在中间）
- 把彩色图片变成黑白的（方便计算）

**为什么这样做：**
- 燃气表数字区域通常在图像中心
- 避免边缘噪声和无关信息的干扰
- 黑白图片计算更快，专注于亮度信息

### 第2步：检查边缘清晰度
- 看数字的轮廓线是否清晰锐利
- 模糊的图片边缘会很模糊，清晰的图片边缘很锐利

**检测原理：**
- 使用Sobel算子检测像素强度变化
- 清晰图像在边缘处有明显的强度跳变
- 模糊图像的强度变化较为平缓

### 第3步：检查对比度
- 看数字和背景的颜色差别大不大
- 差别大 = 容易识别，差别小 = 难识别

**检测内容：**
- 局部对比度：数字区域内的明暗差异
- 全局对比度：整张图片的明暗差异
- 高对比度有助于数字与背景的区分

### 第4步：检查亮度
- 看图片是否太暗或太亮
- 太暗看不清，太亮会反光，都影响识别

**亮度标准：**
- 理想范围：100-180（给20%奖励分）
- 可接受范围：80-200（正常分数）
- 其他范围：太暗或太亮（扣20%分数）

### 第5步：检查细节丰富度
- 看图片是否有足够的细节信息
- 模糊图片细节少，清晰图片细节多

**什么会影响细节：**
- **焦点模糊**会让细节消失
- **运动模糊**会让细节变少
- **压缩过度**会丢失细节
- 细节越多 = 图片越清晰 = 越适合识别数字

**检测方法：**
- 使用拉普拉斯算子检测图像中的快速变化
- 清晰图像的拉普拉斯方差较大
- 模糊图像的拉普拉斯方差较小

### 第6步：专门检测文字区域
- **把图片分成4个角落区域（左上、右上、左下、右下）**
- **在每个区域专门检测文字的边缘清晰度**
- **看文字轮廓是否足够清晰**
- **把4个区域的结果平均，得到文字清晰度分数**

**为什么检测4个角落：**
- 燃气表上可能有多个数字显示区域
- 不只看中央，也要看边缘是否有清晰的文字
- 避免只看一个地方导致判断不准

### 第7步：边缘密度检测
- 使用Canny边缘检测算法计算边缘密度
- 检测图像中真正的边缘，抑制噪声
- 边缘密度反映图像的纹理复杂程度

**边缘类型：**
- **强边缘**：数字轮廓线（黑白对比明显）
- **弱边缘**：阴影、划痕（对比度不明显）

### 第8步：综合打分
把上面所有检查结果按重要性加权平均：

- **文字清晰度：25%**（最重要，直接关系识别效果）
- **局部边缘：20%**（数字区域的边缘质量）
- **全局边缘：15%**（整体图像边缘质量）
- **局部对比度：15%**（数字区域对比度）
- **全局对比度：15%**（整体图像对比度）
- **边缘密度：10%**（细节丰富程度）

### 第9步：亮度质量调整
根据亮度情况对最终得分进行调整：
- 理想亮度（100-180）：给20%奖励
- 可接受亮度（80-200）：不加分不扣分
- 其他亮度：扣20%分数

### 第10步：给出最终结论
- **0.85以上 = 优秀**（图像质量很高，完全适合数字识别）
- **0.75-0.85 = 良好**（图像质量较好，基本满足识别需求）
- **0.6-0.75 = 一般**（图像质量一般，可能影响识别准确率）
- **0.6以下 = 较差**（图像质量差，不建议用于数字识别）

## 检测重点总结

程序像人眼一样，从多个角度检查图片质量：

1. **清晰度**：边缘是否锐利，细节是否丰富
2. **对比度**：数字与背景区分是否明显
3. **亮度**：光照条件是否适合识别
4. **文字质量**：专门检查文字区域的清晰程度
5. **整体评估**：综合所有指标给出最终评分

**最终目标：确保图片质量足够好，能够准确识别燃气表上的数字。**
