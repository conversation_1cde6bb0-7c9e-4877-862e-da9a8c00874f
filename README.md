# 燃气表图像质量检测系统

一个专门用于检测燃气表图像质量的Python工具，帮助判断图像是否适合进行数字识别。

## 功能特点

- 🔍 **图像质量检测** - 分析图像清晰度、对比度、亮度
- 📊 **批量处理** - 支持文件夹内所有图像批量分析
- 📈 **可视化报告** - 生成详细的分析报告和图表
- 🔤 **OCR支持** - 可选的文字识别功能（需安装easyocr）

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python main.py
```

### 3. 选择分析模式
- 单张图像分析
- 两张图像对比
- 文件夹批量分析

## 使用示例

**命令行方式：**
```bash
python src/gas_meter_detector.py 图像路径.jpg
```

**程序输出：**
```
🔍 燃气表图像质量检测结果
=====================================
文件名: meter_001.jpg
OCR适用性: ✅ 适合
综合得分: 0.823
质量等级: 良好
主要问题: 无
改进建议: 图像质量良好，无需改进
```

## 项目结构

```
├── main.py                    # 主程序入口
├── src/
│   ├── gas_meter_detector.py  # 核心检测模块
│   └── ocr_visualization.py   # 可视化模块
├── docs/                      # 文档目录
├── requirements.txt           # 依赖包
└── 燃气表检测配置.json         # 配置文件
```

## 配置说明

修改 `燃气表检测配置.json` 可调整检测参数：

```json
{
  "quality_thresholds": {
    "excellent": 0.85,  // 优秀阈值
    "good": 0.75,       // 良好阈值
    "fair": 0.6         // 一般阈值
  }
}
```

## 依赖包

- `opencv-python` - 图像处理
- `numpy` - 数值计算
- `matplotlib` - 图表生成
- `easyocr` - OCR功能（可选）

## 许可证

MIT License