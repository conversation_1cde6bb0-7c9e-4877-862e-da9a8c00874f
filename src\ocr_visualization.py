#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR可视化工具
用于显示OCR检测结果和文字区域标注
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Any
import matplotlib.patches as patches
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def visualize_ocr_results(image_path: str, analysis_result: Dict[str, Any], 
                         output_dir: str = "output_results", save_image: bool = True) -> str:
    """
    可视化OCR检测结果
    
    Args:
        image_path: 原始图像路径
        analysis_result: 分析结果
        output_dir: 输出目录
        save_image: 是否保存图像
        
    Returns:
        保存的图像路径
    """
    # 读取原始图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图像: {image_path}")
    
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 获取OCR分析结果
    ocr_text_analysis = analysis_result.get("ocr_analysis", {}).get("ocr_text_analysis", {})
    
    if not ocr_text_analysis.get("text_clarity_available", False):
        print("⚠️ 没有可用的OCR文字识别结果")
        return ""
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'OCR文字识别结果分析: {Path(image_path).name}', fontsize=16, fontweight='bold')
    
    # 1. 原始图像 + ROI标注
    ax1 = axes[0, 0]
    ax1.imshow(image_rgb)
    ax1.set_title('原始图像 + ROI区域标注', fontsize=14, fontweight='bold')
    ax1.axis('off')
    
    # 标注ROI区域
    roi_details = ocr_text_analysis.get("roi_detection", {}).get("roi_details", [])
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
    
    for i, roi in enumerate(roi_details):
        x, y, w, h = roi["bbox"]
        color = colors[i % len(colors)]
        rect = patches.Rectangle((x, y), w, h, linewidth=2, edgecolor=color, 
                               facecolor='none', alpha=0.8)
        ax1.add_patch(rect)
        ax1.text(x, y-5, f'ROI-{roi["id"]}', color=color, fontsize=10, fontweight='bold')
    
    # 2. 文字识别结果标注
    ax2 = axes[0, 1]
    ax2.imshow(image_rgb)
    ax2.set_title('文字识别结果标注', fontsize=14, fontweight='bold')
    ax2.axis('off')
    
    # 标注识别出的文字
    text_results = ocr_text_analysis.get("ocr_recognition", {}).get("text_results", [])
    for i, text_result in enumerate(text_results):
        x, y, w, h = text_result["roi_bbox"]
        color = colors[i % len(colors)]
        
        # 绘制边框
        rect = patches.Rectangle((x, y), w, h, linewidth=2, edgecolor=color, 
                               facecolor=color, alpha=0.2)
        ax2.add_patch(rect)
        
        # 显示识别的文字
        texts = [t["text"] for t in text_result["texts"]]
        if texts:
            display_text = "，".join(texts[:2])  # 最多显示2个文字
            if len(texts) > 2:
                display_text += "..."
            ax2.text(x, y-5, display_text, color=color, fontsize=9, 
                    fontweight='bold', bbox=dict(boxstyle="round,pad=0.3", 
                    facecolor='white', alpha=0.8))
    
    # 3. 文字清晰度分析图表
    ax3 = axes[1, 0]
    if text_results:
        roi_ids = [f"ROI-{tr['roi_id']}" for tr in text_results]
        clarity_scores = [tr.get('clarity_score', 0) for tr in ocr_text_analysis.get("clarity_details", [])]
        confidence_scores = [tr["avg_confidence"] for tr in text_results]
        
        x_pos = np.arange(len(roi_ids))
        width = 0.35
        
        bars1 = ax3.bar(x_pos - width/2, clarity_scores, width, label='清晰度得分', alpha=0.8)
        bars2 = ax3.bar(x_pos + width/2, confidence_scores, width, label='OCR置信度', alpha=0.8)
        
        ax3.set_xlabel('ROI区域')
        ax3.set_ylabel('得分')
        ax3.set_title('文字清晰度与OCR置信度对比')
        ax3.set_xticks(x_pos)
        ax3.set_xticklabels(roi_ids, rotation=45)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 1.0)
        
        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.2f}', ha='center', va='bottom', fontsize=8)
        
        for bar in bars2:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.2f}', ha='center', va='bottom', fontsize=8)
    else:
        ax3.text(0.5, 0.5, '未检测到文字区域', ha='center', va='center', 
                transform=ax3.transAxes, fontsize=14)
        ax3.set_title('文字清晰度分析')
    
    # 4. 统计信息表格
    ax4 = axes[1, 1]
    ax4.axis('off')
    ax4.set_title('OCR分析统计信息', fontsize=14, fontweight='bold')
    
    # 准备统计数据
    stats_data = []
    stats_data.append(['总ROI区域数', str(ocr_text_analysis.get("roi_detection", {}).get("total_rois", 0))])
    stats_data.append(['成功识别区域数', str(ocr_text_analysis.get("total_text_regions", 0))])
    stats_data.append(['包含数字区域数', str(ocr_text_analysis.get("digit_regions_count", 0))])
    stats_data.append(['平均OCR置信度', f"{ocr_text_analysis.get('average_confidence', 0):.3f}"])
    stats_data.append(['文字清晰度得分', f"{ocr_text_analysis.get('text_clarity_score', 0):.3f}"])
    stats_data.append(['文字质量等级', ocr_text_analysis.get('text_quality_grade', '未知')])
    
    # 添加识别出的文字内容
    all_texts = []
    for text_result in text_results:
        for text_item in text_result["texts"]:
            all_texts.append(text_item["text"])
    
    if all_texts:
        stats_data.append(['识别文字内容', '，'.join(all_texts[:5])])  # 最多显示5个
        if len(all_texts) > 5:
            stats_data.append(['', f'...等共{len(all_texts)}个文字'])
    
    # 创建表格
    table = ax4.table(cellText=stats_data, 
                     colLabels=['项目', '值'],
                     cellLoc='left',
                     loc='center',
                     colWidths=[0.4, 0.6])
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(stats_data) + 1):
        for j in range(2):
            cell = table[(i, j)]
            if i == 0:  # 表头
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    plt.tight_layout()
    
    if save_image:
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        filename = f"ocr_analysis_{Path(image_path).stem}.png"
        save_path = output_path / filename
        plt.savefig(save_path, dpi=150, bbox_inches='tight', facecolor='white')
        print(f"✅ OCR可视化结果已保存至: {save_path}")
        plt.close()
        return str(save_path)
    else:
        plt.show()
        return ""

def create_comprehensive_visual_analysis(image_path: str, analysis_result: Dict[str, Any],
                                       output_dir: str = "output_results", save_image: bool = True) -> str:
    """
    创建全面的图像质量分析可视化界面（类似您提供的示例）

    Args:
        image_path: 原始图像路径
        analysis_result: 分析结果
        output_dir: 输出目录
        save_image: 是否保存图像

    Returns:
        保存的图像路径
    """
    # 读取原始图像
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图像: {image_path}")

    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 获取分析结果
    file_info = analysis_result.get("file_info", {})
    if not isinstance(file_info, dict):
        file_info = {}

    # 确保file_info包含必要的字段
    if 'width' not in file_info or 'height' not in file_info:
        height, width = gray.shape
        file_info['width'] = width
        file_info['height'] = height

    quality_analysis = analysis_result.get("quality_analysis", {})
    if not isinstance(quality_analysis, dict):
        quality_analysis = {}

    digit_clarity = quality_analysis.get("digit_clarity_check", {})
    if not isinstance(digit_clarity, dict):
        digit_clarity = {}

    lighting_check = quality_analysis.get("lighting_check", {})
    if not isinstance(lighting_check, dict):
        lighting_check = {}

    summary = analysis_result.get("summary", {})
    if not isinstance(summary, dict):
        summary = {}

    # 创建图像质量分析界面 - 调整尺寸适应2行布局
    fig = plt.figure(figsize=(20, 10))

    # 设置主标题
    filename = Path(image_path).stem
    fig.suptitle(f'图像质量分析: {filename}', fontsize=18, fontweight='bold', y=0.95)

    # 创建网格布局 (2行4列) - 删除了统计图表
    gs = fig.add_gridspec(2, 4, height_ratios=[1, 1], width_ratios=[1, 1, 1, 1],
                         hspace=0.3, wspace=0.3)

    # 1. 原始图像
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.imshow(image_rgb)
    ax1.set_title('原始图像', fontsize=12, fontweight='bold')
    ax1.axis('off')

    # 2. 中央区域（数字区域）
    ax2 = fig.add_subplot(gs[0, 1])
    # 提取中央区域
    h, w = gray.shape
    crop_ratio = 0.6  # 中央60%区域
    center_h, center_w = int(h * crop_ratio), int(w * crop_ratio)
    start_h, start_w = (h - center_h) // 2, (w - center_w) // 2
    center_region = image_rgb[start_h:start_h+center_h, start_w:start_w+center_w]
    ax2.imshow(center_region)
    ax2.set_title('中央区域 (数字区域)', fontsize=12, fontweight='bold')
    ax2.axis('off')

    # 3. 亮度分布直方图
    ax3 = fig.add_subplot(gs[0, 2:])
    brightness_values = gray.flatten()
    # 计算中央区域亮度
    center_gray = gray[start_h:start_h+center_h, start_w:start_w+center_w]
    center_brightness = center_gray.flatten()

    ax3.hist(brightness_values, bins=50, alpha=0.7, color='blue', label='全图', density=True)
    ax3.hist(center_brightness, bins=50, alpha=0.7, color='red', label='中央区域', density=True)
    ax3.set_xlabel('亮度值')
    ax3.set_ylabel('频率')
    ax3.set_title('亮度分布直方图', fontsize=12, fontweight='bold')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 边缘检测 (Canny)
    ax4 = fig.add_subplot(gs[1, 0])
    edges = cv2.Canny(gray, 50, 150)
    ax4.imshow(edges, cmap='gray')
    ax4.set_title('边缘检测 (Canny)', fontsize=12, fontweight='bold')
    ax4.axis('off')

    # 5. 梯度幅值
    ax5 = fig.add_subplot(gs[1, 1])
    sobel_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    gradient_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
    im5 = ax5.imshow(gradient_magnitude, cmap='hot')
    ax5.set_title('梯度幅值', fontsize=12, fontweight='bold')
    ax5.axis('off')
    plt.colorbar(im5, ax=ax5, fraction=0.046, pad=0.04)

    # 6. 拉普拉斯算子 (细节检测)
    ax6 = fig.add_subplot(gs[1, 2])
    laplacian = cv2.Laplacian(gray, cv2.CV_64F)
    im6 = ax6.imshow(laplacian, cmap='RdBu')
    ax6.set_title('拉普拉斯算子 (细节检测)', fontsize=12, fontweight='bold')
    ax6.axis('off')
    plt.colorbar(im6, ax=ax6, fraction=0.046, pad=0.04)

    # 7. 质量指标雷达图
    ax7 = fig.add_subplot(gs[1, 3], projection='polar')

    # 准备雷达图数据
    categories = ['边缘清晰度', '对比度', '亮度质量', '局部方差', '边缘密度']
    values = [
        min(digit_clarity.get('edge_clarity', 0) / 20.0, 1.0),
        min(digit_clarity.get('contrast', 0) / 60.0, 1.0),
        lighting_check.get('lighting_score', 0),
        min(digit_clarity.get('local_variance', 0) / 100.0, 1.0),
        min(digit_clarity.get('edge_density', 0) * 20, 1.0)
    ]

    # 闭合雷达图
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    values += values[:1]
    angles += angles[:1]

    ax7.plot(angles, values, 'o-', linewidth=2, color='blue')
    ax7.fill(angles, values, alpha=0.25, color='blue')
    ax7.set_xticks(angles[:-1])
    ax7.set_xticklabels(categories, fontsize=10)
    ax7.set_ylim(0, 1)
    ax7.set_title('质量指标雷达图', fontsize=12, fontweight='bold', pad=20)
    ax7.grid(True)

    plt.tight_layout()

    if save_image:
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        filename = f"visual_analysis_{Path(image_path).stem}.png"
        save_path = output_path / filename
        plt.savefig(save_path, dpi=200, bbox_inches='tight', facecolor='white')
        print(f"✅ 图像质量分析可视化已保存至: {save_path}")
        plt.close()
        return str(save_path)
    else:
        plt.show()
        return ""

def create_comparison_visual_analysis(image1_path: str, image2_path: str,
                                    result1: Dict[str, Any], result2: Dict[str, Any],
                                    output_dir: str = "output_results", save_image: bool = True) -> str:
    """
    创建燃气表图像对比分析可视化界面（类似您提供的示例）

    Args:
        image1_path: 第一张图像路径
        image2_path: 第二张图像路径
        result1: 第一张图像分析结果
        result2: 第二张图像分析结果
        output_dir: 输出目录
        save_image: 是否保存图像

    Returns:
        保存的图像路径
    """
    # 读取两张图像
    image1 = cv2.imread(image1_path)
    image2 = cv2.imread(image2_path)
    if image1 is None or image2 is None:
        raise ValueError("无法读取图像文件")

    image1_rgb = cv2.cvtColor(image1, cv2.COLOR_BGR2RGB)
    image2_rgb = cv2.cvtColor(image2, cv2.COLOR_BGR2RGB)
    gray1 = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY)

    # 获取分析结果并确保类型安全
    quality1 = result1.get("quality_analysis", {})
    if not isinstance(quality1, dict):
        quality1 = {}
    quality2 = result2.get("quality_analysis", {})
    if not isinstance(quality2, dict):
        quality2 = {}

    digit1 = quality1.get("digit_clarity_check", {})
    if not isinstance(digit1, dict):
        digit1 = {}
    digit2 = quality2.get("digit_clarity_check", {})
    if not isinstance(digit2, dict):
        digit2 = {}

    lighting1 = quality1.get("lighting_check", {})
    if not isinstance(lighting1, dict):
        lighting1 = {}
    lighting2 = quality2.get("lighting_check", {})
    if not isinstance(lighting2, dict):
        lighting2 = {}

    summary1 = result1.get("summary", {})
    if not isinstance(summary1, dict):
        summary1 = {}
    summary2 = result2.get("summary", {})
    if not isinstance(summary2, dict):
        summary2 = {}

    # 创建对比分析界面
    fig = plt.figure(figsize=(24, 16))
    fig.suptitle('燃气表图像质量对比分析', fontsize=20, fontweight='bold', y=0.95)

    # 创建网格布局 (4行6列)
    gs = fig.add_gridspec(4, 6, height_ratios=[1, 1, 1, 1], width_ratios=[1, 1, 1, 1, 1, 1],
                         hspace=0.4, wspace=0.3)

    # 第一行：原始图像对比
    ax1 = fig.add_subplot(gs[0, 0:2])
    ax1.imshow(image1_rgb)
    brightness1 = lighting1.get('brightness', 0)
    contrast1 = lighting1.get('contrast', 0)
    score1 = summary1.get('overall_score', 0)
    ax1.set_title(f'图像A - 原始图像\n亮度: {brightness1:.1f}, 对比度: {contrast1:.1f}',
                 fontsize=12, fontweight='bold')
    ax1.axis('off')

    ax2 = fig.add_subplot(gs[0, 2:4])
    ax2.imshow(image2_rgb)
    brightness2 = lighting2.get('brightness', 0)
    contrast2 = lighting2.get('contrast', 0)
    score2 = summary2.get('overall_score', 0)
    ax2.set_title(f'图像B - 原始图像\n亮度: {brightness2:.1f}, 对比度: {contrast2:.1f}',
                 fontsize=12, fontweight='bold')
    ax2.axis('off')

    # 整体亮度分布对比
    ax3 = fig.add_subplot(gs[0, 4:])
    brightness_values1 = gray1.flatten()
    brightness_values2 = gray2.flatten()
    ax3.hist(brightness_values1, bins=50, alpha=0.7, color='blue', label='图像A', density=True)
    ax3.hist(brightness_values2, bins=50, alpha=0.7, color='red', label='图像B', density=True)
    ax3.set_xlabel('亮度值')
    ax3.set_ylabel('频率')
    ax3.set_title('整体亮度分布对比', fontsize=12, fontweight='bold')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 第二行：中央区域对比
    # 提取中央区域
    h1, w1 = gray1.shape
    h2, w2 = gray2.shape
    crop_ratio = 0.6

    center_h1, center_w1 = int(h1 * crop_ratio), int(w1 * crop_ratio)
    start_h1, start_w1 = (h1 - center_h1) // 2, (w1 - center_w1) // 2
    center_region1 = image1_rgb[start_h1:start_h1+center_h1, start_w1:start_w1+center_w1]
    center_gray1 = gray1[start_h1:start_h1+center_h1, start_w1:start_w1+center_w1]

    center_h2, center_w2 = int(h2 * crop_ratio), int(w2 * crop_ratio)
    start_h2, start_w2 = (h2 - center_h2) // 2, (w2 - center_w2) // 2
    center_region2 = image2_rgb[start_h2:start_h2+center_h2, start_w2:start_w2+center_w2]
    center_gray2 = gray2[start_h2:start_h2+center_h2, start_w2:start_w2+center_w2]

    ax4 = fig.add_subplot(gs[1, 0:2])
    ax4.imshow(center_region1)
    center_brightness1 = np.mean(center_gray1)
    center_contrast1 = np.std(center_gray1)
    ax4.set_title(f'图像A - 中央区域\n亮度: {center_brightness1:.1f}, 对比度: {center_contrast1:.1f}',
                 fontsize=12, fontweight='bold')
    ax4.axis('off')

    ax5 = fig.add_subplot(gs[1, 2:4])
    ax5.imshow(center_region2)
    center_brightness2 = np.mean(center_gray2)
    center_contrast2 = np.std(center_gray2)
    ax5.set_title(f'图像B - 中央区域\n亮度: {center_brightness2:.1f}, 对比度: {center_contrast2:.1f}',
                 fontsize=12, fontweight='bold')
    ax5.axis('off')

    # 中央区域亮度分布对比
    ax6 = fig.add_subplot(gs[1, 4:])
    center_brightness_values1 = center_gray1.flatten()
    center_brightness_values2 = center_gray2.flatten()
    ax6.hist(center_brightness_values1, bins=30, alpha=0.7, color='blue', label='图像A', density=True)
    ax6.hist(center_brightness_values2, bins=30, alpha=0.7, color='red', label='图像B', density=True)
    ax6.set_xlabel('亮度值')
    ax6.set_ylabel('频率')
    ax6.set_title('中央区域亮度分布对比', fontsize=12, fontweight='bold')
    ax6.legend()
    ax6.grid(True, alpha=0.3)

    # 第三行：边缘检测对比
    ax7 = fig.add_subplot(gs[2, 0])
    edges1 = cv2.Canny(gray1, 50, 150)
    edge_density1 = digit1.get('edge_density', 0)
    ax7.imshow(edges1, cmap='gray')
    ax7.set_title(f'图像A - 边缘检测\n边缘密度: {edge_density1:.4f}', fontsize=10, fontweight='bold')
    ax7.axis('off')

    ax8 = fig.add_subplot(gs[2, 1])
    edges2 = cv2.Canny(gray2, 50, 150)
    edge_density2 = digit2.get('edge_density', 0)
    ax8.imshow(edges2, cmap='gray')
    ax8.set_title(f'图像B - 边缘检测\n边缘密度: {edge_density2:.4f}', fontsize=10, fontweight='bold')
    ax8.axis('off')

    # 边缘密度对比
    ax9 = fig.add_subplot(gs[2, 2])
    edge_comparison = [edge_density1, edge_density2]
    bars = ax9.bar(['图像A', '图像B'], edge_comparison, color=['blue', 'red'], alpha=0.7)
    ax9.set_ylabel('边缘密度')
    ax9.set_title('边缘密度对比', fontsize=10, fontweight='bold')
    ax9.grid(True, alpha=0.3)
    for bar, value in zip(bars, edge_comparison):
        ax9.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.0001,
                f'{value:.4f}', ha='center', va='bottom', fontsize=9)

    # 梯度幅值对比
    ax10 = fig.add_subplot(gs[2, 3])
    sobel_x1 = cv2.Sobel(gray1, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y1 = cv2.Sobel(gray1, cv2.CV_64F, 0, 1, ksize=3)
    gradient1 = np.sqrt(sobel_x1**2 + sobel_y1**2)
    gradient_mean1 = np.mean(gradient1)
    ax10.imshow(gradient1, cmap='hot')
    ax10.set_title(f'图像A - 梯度幅值\n平均梯度: {gradient_mean1:.1f}', fontsize=10, fontweight='bold')
    ax10.axis('off')

    ax11 = fig.add_subplot(gs[2, 4])
    sobel_x2 = cv2.Sobel(gray2, cv2.CV_64F, 1, 0, ksize=3)
    sobel_y2 = cv2.Sobel(gray2, cv2.CV_64F, 0, 1, ksize=3)
    gradient2 = np.sqrt(sobel_x2**2 + sobel_y2**2)
    gradient_mean2 = np.mean(gradient2)
    ax11.imshow(gradient2, cmap='hot')
    ax11.set_title(f'图像B - 梯度幅值\n平均梯度: {gradient_mean2:.1f}', fontsize=10, fontweight='bold')
    ax11.axis('off')

    # 拉普拉斯方差对比（细节丰富度）
    ax12 = fig.add_subplot(gs[2, 5])
    laplacian_var1 = digit1.get('local_variance', 0)
    laplacian_var2 = digit2.get('local_variance', 0)
    variance_comparison = [laplacian_var1, laplacian_var2]
    bars = ax12.bar(['图像A', '图像B'], variance_comparison, color=['blue', 'red'], alpha=0.7)
    ax12.set_ylabel('拉普拉斯方差')
    ax12.set_title('拉普拉斯方差对比 (细节丰富度)', fontsize=10, fontweight='bold')
    ax12.grid(True, alpha=0.3)
    for bar, value in zip(bars, variance_comparison):
        ax12.text(bar.get_x() + bar.get_width()/2., bar.get_height() + max(variance_comparison)*0.01,
                f'{value:.1f}', ha='center', va='bottom', fontsize=9)

    # 第四行：综合质量对比
    ax13 = fig.add_subplot(gs[3, 0:2])
    quality_metrics = ['数字清晰度', '光线质量', '整体质量']
    scores1 = [digit1.get('digit_clarity_score', 0), lighting1.get('lighting_score', 0), summary1.get('overall_score', 0)]
    scores2 = [digit2.get('digit_clarity_score', 0), lighting2.get('lighting_score', 0), summary2.get('overall_score', 0)]

    x = np.arange(len(quality_metrics))
    width = 0.35

    bars1 = ax13.bar(x - width/2, scores1, width, label='图像A', color='blue', alpha=0.7)
    bars2 = ax13.bar(x + width/2, scores2, width, label='图像B', color='red', alpha=0.7)

    ax13.set_ylabel('得分')
    ax13.set_title('质量指标对比', fontsize=12, fontweight='bold')
    ax13.set_xticks(x)
    ax13.set_xticklabels(quality_metrics)
    ax13.legend()
    ax13.grid(True, alpha=0.3)
    ax13.set_ylim(0, 1)

    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax13.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

    # 详细对比表格
    ax14 = fig.add_subplot(gs[3, 2:])
    ax14.axis('off')
    ax14.set_title('详细对比数据', fontsize=12, fontweight='bold')

    # 准备表格数据
    comparison_data = [
        ['平均亮度', f"{brightness1:.1f}", f"{brightness2:.1f}", f"{brightness2-brightness1:+.1f}"],
        ['对比度', f"{contrast1:.1f}", f"{contrast2:.1f}", f"{contrast2-contrast1:+.1f}"],
        ['边缘密度', f"{edge_density1:.4f}", f"{edge_density2:.4f}", f"{edge_density2-edge_density1:+.4f}"],
        ['局部方差', f"{laplacian_var1:.1f}", f"{laplacian_var2:.1f}", f"{laplacian_var2-laplacian_var1:+.1f}"],
        ['数字清晰度', f"{scores1[0]:.3f}", f"{scores2[0]:.3f}", f"{scores2[0]-scores1[0]:+.3f}"],
        ['光线质量', f"{scores1[1]:.3f}", f"{scores2[1]:.3f}", f"{scores2[1]-scores1[1]:+.3f}"],
        ['整体质量', f"{scores1[2]:.3f}", f"{scores2[2]:.3f}", f"{scores2[2]-scores1[2]:+.3f}"]
    ]

    # 创建表格
    table = ax14.table(cellText=comparison_data,
                      colLabels=['指标', '图像A', '图像B', '差值'],
                      cellLoc='center',
                      loc='center',
                      colWidths=[0.25, 0.2, 0.2, 0.2])

    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)

    # 设置表格样式
    for i in range(len(comparison_data) + 1):
        for j in range(4):
            cell = table[(i, j)]
            if i == 0:  # 表头
                cell.set_facecolor('#2E86AB')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f8f9fa' if i % 2 == 0 else 'white')
                # 根据差值设置颜色
                if j == 3 and i > 0:  # 差值列
                    diff_value = float(comparison_data[i-1][3])
                    if diff_value > 0:
                        cell.set_facecolor('#d4edda')  # 绿色表示改善
                    elif diff_value < 0:
                        cell.set_facecolor('#f8d7da')  # 红色表示下降

    plt.tight_layout()

    if save_image:
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        filename = f"comparison_analysis_{Path(image1_path).stem}_vs_{Path(image2_path).stem}.png"
        save_path = output_path / filename
        plt.savefig(save_path, dpi=200, bbox_inches='tight', facecolor='white')
        print(f"✅ 对比分析可视化已保存至: {save_path}")
        plt.close()
        return str(save_path)
    else:
        plt.show()
        return ""

def create_ocr_summary_report(analysis_results: List[Dict[str, Any]],
                             output_dir: str = "output_results") -> str:
    """
    创建OCR分析汇总报告
    
    Args:
        analysis_results: 多个图像的分析结果列表
        output_dir: 输出目录
        
    Returns:
        报告文件路径
    """
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = output_path / f"ocr_summary_report_{timestamp}.md"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# 🔍 OCR文字识别分析汇总报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 统计概览
        total_images = len(analysis_results)
        ocr_available_count = 0
        text_detected_count = 0
        digit_detected_count = 0
        high_quality_count = 0
        
        for result in analysis_results:
            ocr_analysis = result.get("ocr_analysis", {}).get("ocr_text_analysis", {})
            if ocr_analysis.get("text_clarity_available", False):
                ocr_available_count += 1
                if ocr_analysis.get("total_text_regions", 0) > 0:
                    text_detected_count += 1
                if ocr_analysis.get("digit_regions_count", 0) > 0:
                    digit_detected_count += 1
                if ocr_analysis.get("text_clarity_score", 0) >= 0.7:
                    high_quality_count += 1
        
        f.write("## 📊 统计概览\n\n")
        f.write(f"- **总图像数量**: {total_images}\n")
        f.write(f"- **OCR功能可用**: {ocr_available_count} ({ocr_available_count/total_images*100:.1f}%)\n")
        f.write(f"- **检测到文字**: {text_detected_count} ({text_detected_count/total_images*100:.1f}%)\n")
        f.write(f"- **检测到数字**: {digit_detected_count} ({digit_detected_count/total_images*100:.1f}%)\n")
        f.write(f"- **高质量文字**: {high_quality_count} ({high_quality_count/total_images*100:.1f}%)\n\n")
        
        # 详细结果
        f.write("## 📋 详细分析结果\n\n")
        f.write("| 图像文件 | OCR可用 | 文字区域数 | 数字区域数 | 清晰度得分 | 质量等级 | 识别文字 |\n")
        f.write("|----------|---------|------------|------------|------------|----------|----------|\n")
        
        for result in analysis_results:
            file_name = result.get("file_info", {}).get("file_name", "未知")
            ocr_analysis = result.get("ocr_analysis", {}).get("ocr_text_analysis", {})
            
            if ocr_analysis.get("text_clarity_available", False):
                ocr_available = "✅"
                text_regions = ocr_analysis.get("total_text_regions", 0)
                digit_regions = ocr_analysis.get("digit_regions_count", 0)
                clarity_score = ocr_analysis.get("text_clarity_score", 0)
                quality_grade = ocr_analysis.get("text_quality_grade", "未知")
                
                # 提取识别的文字
                text_results = ocr_analysis.get("ocr_recognition", {}).get("text_results", [])
                all_texts = []
                for text_result in text_results:
                    for text_item in text_result["texts"]:
                        all_texts.append(text_item["text"])
                recognized_text = "，".join(all_texts[:3])  # 最多显示3个
                if len(all_texts) > 3:
                    recognized_text += "..."
                if not recognized_text:
                    recognized_text = "无"
            else:
                ocr_available = "❌"
                text_regions = 0
                digit_regions = 0
                clarity_score = 0
                quality_grade = "不可用"
                recognized_text = "不可用"
            
            f.write(f"| {file_name} | {ocr_available} | {text_regions} | {digit_regions} | "
                   f"{clarity_score:.3f} | {quality_grade} | {recognized_text} |\n")
        
        f.write("\n## 💡 改进建议\n\n")
        
        if text_detected_count < total_images * 0.5:
            f.write("- 📸 **拍摄建议**: 超过50%的图像未检测到文字，建议调整拍摄角度确保文字区域清晰可见\n")
        
        if digit_detected_count < total_images * 0.3:
            f.write("- 🔢 **数字识别**: 数字检测率较低，建议重点关注燃气表数字显示区域的拍摄质量\n")
        
        if high_quality_count < total_images * 0.6:
            f.write("- 💡 **质量提升**: 建议改善光线条件和拍摄清晰度，提高OCR识别准确性\n")
        
        f.write("\n---\n")
        f.write("*报告由燃气表图像质量检测系统自动生成*\n")
    
    print(f"✅ OCR汇总报告已保存至: {report_path}")
    return str(report_path)
